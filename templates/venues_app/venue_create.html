{% extends 'base.html' %}

{% block title %}Create Your Venue - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Creation */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venue Creation Section */
    .venue-creation-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .venue-creation-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header */
    .venue-header {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .venue-header-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--cw-shadow-md);
        flex-shrink: 0;
    }

    .venue-header-icon i {
        font-size: 2rem;
        color: white;
    }

    .venue-header-content h1 {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .venue-header-content p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .back-button {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-button:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Form Sections */
    .form-section-cw {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .form-section-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .section-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    /* Form Styling */
    .form-group-cw {
        margin-bottom: 2rem;
    }

    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
        display: block;
    }

    .form-control-cw, .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        width: 100%;
    }

    .form-control-cw:focus, .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        outline: none;
    }

    .form-control-cw.is-invalid, .form-select-cw.is-invalid {
        border-color: #dc2626;
    }

    .required-cw {
        color: #dc2626;
        font-weight: 600;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: block;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
        font-size: 1.1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Venue Status Options */
    .venue-status-options {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .status-option {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        background: white;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .status-option:hover {
        border-color: var(--cw-brand-primary);
        background: var(--cw-accent-light);
    }

    .status-option.selected {
        border-color: var(--cw-brand-primary);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-md);
    }

    .status-option input[type="radio"] {
        margin-right: 1rem;
        transform: scale(1.2);
    }

    .status-option-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .status-option-description {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    /* Submit Section */
    .submit-section {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        text-align: center;
        box-shadow: var(--cw-shadow-md);
    }

    .submit-note {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        margin-top: 1.5rem;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        border-left: 4px solid var(--cw-brand-accent);
    }

    /* Alert Styling */
    .alert-cw-danger {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .alert-cw-danger h6 {
        color: #991b1b;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .venue-creation-container {
            padding: 0 0.5rem;
        }

        .venue-header {
            flex-direction: column;
            text-align: center;
            padding: 1.5rem;
        }

        .venue-header-content h1 {
            font-size: 1.75rem;
        }

        .venue-header-icon {
            width: 64px;
            height: 64px;
        }

        .venue-header-icon i {
            font-size: 1.5rem;
        }

        .form-section-cw {
            padding: 1.5rem;
        }

        .section-title-cw {
            font-size: 1.25rem;
        }

        .btn-cw-primary {
            padding: 0.875rem 2rem;
            font-size: 1rem;
        }

        .status-option {
            padding: 1rem;
        }

        .status-option-title {
            font-size: 1rem;
        }

        .status-option-description {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-creation-section">
    <div class="venue-creation-container">
        <!-- Header -->
        <div class="venue-header">
            <a href="{% url 'accounts_app:service_provider_profile' %}" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to Profile
            </a>
            <div class="venue-header-icon">
                <i class="fas fa-store"></i>
            </div>
            <div class="venue-header-content">
                <h1>Create Your Venue</h1>
                <p>Set up your business venue to start attracting customers on CozyWish</p>
            </div>
        </div>

        <form method="post">
            {% csrf_token %}

            <!-- Form-wide errors -->
            {% if form.non_field_errors %}
                <div class="alert-cw-danger">
                    <h6>Please correct the following errors:</h6>
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-info-circle"></i>
                    Basic Information
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Venue Name <span class="required-cw">*</span>
                    </label>
                    {% if form.venue_name.errors %}
                        {{ form.venue_name|add_class:"form-control-cw is-invalid" }}
                        <div class="invalid-feedback-cw">{{ form.venue_name.errors.0 }}</div>
                    {% else %}
                        {{ form.venue_name|add_class:"form-control-cw" }}
                    {% endif %}
                    {% if form.venue_name.help_text %}
                        <div class="form-text-cw">{{ form.venue_name.help_text }}</div>
                    {% endif %}
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Short Description <span class="required-cw">*</span>
                    </label>
                    {% if form.short_description.errors %}
                        {{ form.short_description|add_class:"form-control-cw is-invalid" }}
                        <div class="invalid-feedback-cw">{{ form.short_description.errors.0 }}</div>
                    {% else %}
                        {{ form.short_description|add_class:"form-control-cw" }}
                    {% endif %}
                    {% if form.short_description.help_text %}
                        <div class="form-text-cw">{{ form.short_description.help_text }}</div>
                    {% endif %}
                    <div class="form-text-cw">Briefly describe your venue and the services you offer (minimum 10 characters, maximum 500 characters)</div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-map-marker-alt"></i>
                    Location
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                State <span class="required-cw">*</span>
                            </label>
                            {% if form.state.errors %}
                                {{ form.state|add_class:"form-control-cw is-invalid" }}
                                <div class="invalid-feedback-cw">{{ form.state.errors.0 }}</div>
                            {% else %}
                                {{ form.state|add_class:"form-control-cw" }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                County <span class="required-cw">*</span>
                            </label>
                            {% if form.county.errors %}
                                {{ form.county|add_class:"form-control-cw is-invalid" }}
                                <div class="invalid-feedback-cw">{{ form.county.errors.0 }}</div>
                            {% else %}
                                {{ form.county|add_class:"form-control-cw" }}
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        City <span class="required-cw">*</span>
                    </label>
                    {% if form.city.errors %}
                        {{ form.city|add_class:"form-control-cw is-invalid" }}
                        <div class="invalid-feedback-cw">{{ form.city.errors.0 }}</div>
                    {% else %}
                        {{ form.city|add_class:"form-control-cw" }}
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Number <span class="required-cw">*</span>
                            </label>
                            {% if form.street_number.errors %}
                                {{ form.street_number|add_class:"form-control-cw is-invalid" }}
                                <div class="invalid-feedback-cw">{{ form.street_number.errors.0 }}</div>
                            {% else %}
                                {{ form.street_number|add_class:"form-control-cw" }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Name <span class="required-cw">*</span>
                            </label>
                            {% if form.street_name.errors %}
                                {{ form.street_name|add_class:"form-control-cw is-invalid" }}
                                <div class="invalid-feedback-cw">{{ form.street_name.errors.0 }}</div>
                            {% else %}
                                {{ form.street_name|add_class:"form-control-cw" }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Venue Status Selection -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-check-circle"></i>
                    Venue Status
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Choose how to save your venue <span class="required-cw">*</span>
                    </label>
                    <div class="form-text-cw mb-3">You can save as draft to edit later, or submit for admin approval to make it visible to customers</div>

                    {% if form.venue_status.errors %}
                        <div class="invalid-feedback-cw mb-3">{{ form.venue_status.errors.0 }}</div>
                    {% endif %}

                    <div class="venue-status-options">
                        {% for choice in form.venue_status %}
                            <div class="status-option {% if choice.data.selected %}selected{% endif %}" onclick="selectStatusOption(this, '{{ choice.data.value }}')">
                                {{ choice.tag }}
                                <div class="status-option-content">
                                    <div class="status-option-title">{{ choice.choice_label }}</div>
                                    {% if choice.data.value == 'draft' %}
                                        <div class="status-option-description">
                                            Save your venue privately. You can edit all details (description, images, categories, operating hours) and submit for approval when ready.
                                        </div>
                                    {% else %}
                                        <div class="status-option-description">
                                            Submit your venue for admin review. Once approved, it will be visible to customers. You can still edit details after approval.
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    {% if form.venue_status.help_text %}
                        <div class="form-text-cw">{{ form.venue_status.help_text }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Submit Section -->
            <div class="submit-section">
                <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-save me-2"></i>Create Venue
                </button>
                <div class="submit-note">
                    <i class="fas fa-info-circle me-2"></i>
                    After creating your venue, you can add more details like images, categories, services, and operating hours from your venue management page.
                </div>
            </div>
        </form>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status option selection
    window.selectStatusOption = function(element, value) {
        // Remove selected class from all options
        document.querySelectorAll('.status-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Add selected class to clicked option
        element.classList.add('selected');

        // Check the radio button
        const radioButton = element.querySelector('input[type="radio"]');
        if (radioButton) {
            radioButton.checked = true;
        }
    };

    // Initialize selected state based on checked radio button
    const checkedRadio = document.querySelector('input[name="{{ form.venue_status.name }}"]:checked');
    if (checkedRadio) {
        const parentOption = checkedRadio.closest('.status-option');
        if (parentOption) {
            parentOption.classList.add('selected');
        }
    }

    // Form validation enhancement
    const form = document.querySelector('form');
    const venueNameInput = document.querySelector('input[name="{{ form.venue_name.name }}"]');
    const shortDescriptionInput = document.querySelector('textarea[name="{{ form.short_description.name }}"]');
    const statusInputs = document.querySelectorAll('input[name="{{ form.venue_status.name }}"]');

    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Validate venue name
        if (!venueNameInput.value.trim()) {
            errorMessages.push('Venue name is required.');
            isValid = false;
        }

        // Validate short description
        if (!shortDescriptionInput.value.trim()) {
            errorMessages.push('Short description is required.');
            isValid = false;
        } else if (shortDescriptionInput.value.trim().length < 10) {
            errorMessages.push('Short description must be at least 10 characters long.');
            isValid = false;
        }

        // Validate status selection
        const statusSelected = Array.from(statusInputs).some(input => input.checked);
        if (!statusSelected) {
            errorMessages.push('Please select a venue status.');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please correct the following errors:\n\n' + errorMessages.join('\n'));
        }
    });

    // Character counter for description
    if (shortDescriptionInput) {
        const maxLength = 500;
        const counterDiv = document.createElement('div');
        counterDiv.className = 'form-text-cw mt-2';
        counterDiv.style.textAlign = 'right';
        shortDescriptionInput.parentNode.appendChild(counterDiv);

        function updateCounter() {
            const remaining = maxLength - shortDescriptionInput.value.length;
            counterDiv.textContent = `${shortDescriptionInput.value.length}/${maxLength} characters`;

            if (remaining < 50) {
                counterDiv.style.color = '#dc2626';
            } else {
                counterDiv.style.color = 'var(--cw-neutral-600)';
            }
        }

        shortDescriptionInput.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Auto-resize textarea
    if (shortDescriptionInput) {
        shortDescriptionInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }

    console.log('CozyWish venue creation form loaded successfully');
});
</script>
{% endblock %}
