{% extends 'base.html' %}

{% block title %}Edit {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venue Edit Section */
    .venue-edit-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .venue-edit-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header */
    .venue-header {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .venue-header-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--cw-shadow-md);
        flex-shrink: 0;
    }

    .venue-header-icon i {
        font-size: 2rem;
        color: white;
    }

    .venue-header-content h1 {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .venue-header-content p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .back-button {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-button:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Current Status Card */
    .current-status-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .current-status-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .status-badge-cw {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 0.875rem;
        border: 1px solid var(--cw-brand-primary);
    }

    /* Form Sections */
    .form-section-cw {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .form-section-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .section-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    /* Form Styling */
    .form-group-cw {
        margin-bottom: 2rem;
    }

    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
        display: block;
    }

    .form-control-cw, .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        width: 100%;
    }

    .form-control-cw:focus, .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        outline: none;
    }

    .form-control-cw.is-invalid, .form-select-cw.is-invalid {
        border-color: #dc2626;
    }

    .required-cw {
        color: #dc2626;
        font-weight: 600;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: block;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
        font-size: 1.1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        text-align: center;
    }

    @media (min-width: 768px) {
        .action-buttons {
            flex-direction: row;
            justify-content: center;
            gap: 1rem;
        }
    }

    /* Alert Styling */
    .alert-cw-danger {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .alert-cw-danger h5 {
        color: #991b1b;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .venue-header {
            flex-direction: column;
            text-align: center;
        }

        .venue-header-content h1 {
            font-size: 1.75rem;
        }

        .form-section-cw {
            padding: 1.5rem;
        }

        .section-title-cw {
            font-size: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-edit-section">
    <div class="venue-edit-container">
        <!-- Header -->
        <div class="venue-header">
            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to Details
            </a>
            <div class="venue-header-icon">
                <i class="fas fa-edit"></i>
            </div>
            <div class="venue-header-content">
                <h1>Edit Venue</h1>
                <p>Update your venue information and settings</p>
            </div>
        </div>

        <!-- Current Status -->
        {% if venue %}
        <div class="current-status-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ venue.venue_name }}</h5>
                    <p class="mb-0 text-muted">Current Status:
                        <span class="status-badge-cw">
                            {{ venue.get_approval_status_display }}
                        </span>
                    </p>
                </div>
                <div>
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-secondary btn-sm">
                        <i class="fas fa-eye me-1"></i>View Details
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Display form errors for debugging -->
        {% if form.errors %}
            <div class="alert-cw-danger">
                <h5>Form Errors:</h5>
                <ul>
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            <li><strong>{{ field }}:</strong> {{ error }}</li>
                        {% endfor %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Basic Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-info-circle"></i>
                    Basic Information
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Venue Name <span class="required-cw">*</span>
                    </label>
                    {{ form.venue_name|add_class:"form-control-cw" }}
                    {% if form.venue_name.help_text %}
                        <div class="form-text-cw">{{ form.venue_name.help_text }}</div>
                    {% endif %}
                    {% if form.venue_name.errors %}
                        <div class="invalid-feedback-cw">{{ form.venue_name.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Short Description <span class="required-cw">*</span>
                    </label>
                    {{ form.short_description|add_class:"form-control-cw" }}
                    {% if form.short_description.help_text %}
                        <div class="form-text-cw">{{ form.short_description.help_text }}</div>
                    {% endif %}
                    {% if form.short_description.errors %}
                        <div class="invalid-feedback-cw">{{ form.short_description.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Location Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-map-marker-alt"></i>
                    Location
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                State <span class="required-cw">*</span>
                            </label>
                            {{ form.state|add_class:"form-control-cw" }}
                            {% if form.state.errors %}
                                <div class="invalid-feedback-cw">{{ form.state.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                County <span class="required-cw">*</span>
                            </label>
                            {{ form.county|add_class:"form-control-cw" }}
                            {% if form.county.errors %}
                                <div class="invalid-feedback-cw">{{ form.county.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        City <span class="required-cw">*</span>
                    </label>
                    {{ form.city|add_class:"form-control-cw" }}
                    {% if form.city.errors %}
                        <div class="invalid-feedback-cw">{{ form.city.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Number <span class="required-cw">*</span>
                            </label>
                            {{ form.street_number|add_class:"form-control-cw" }}
                            {% if form.street_number.errors %}
                                <div class="invalid-feedback-cw">{{ form.street_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Street Name <span class="required-cw">*</span>
                            </label>
                            {{ form.street_name|add_class:"form-control-cw" }}
                            {% if form.street_name.errors %}
                                <div class="invalid-feedback-cw">{{ form.street_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories and Tags -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-tags"></i>
                    Categories & Tags
                </h3>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Categories <span class="required-cw">*</span>
                    </label>
                    {{ form.categories }}
                    {% if form.categories.help_text %}
                        <div class="form-text-cw">{{ form.categories.help_text }}</div>
                    {% endif %}
                    {% if form.categories.errors %}
                        <div class="invalid-feedback-cw">{{ form.categories.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Tags
                    </label>
                    {{ form.tags|add_class:"form-control-cw" }}
                    {% if form.tags.help_text %}
                        <div class="form-text-cw">{{ form.tags.help_text }}</div>
                    {% endif %}
                    {% if form.tags.errors %}
                        <div class="invalid-feedback-cw">{{ form.tags.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-phone"></i>
                    Contact Information
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Phone Number
                            </label>
                            {{ form.phone|add_class:"form-control-cw" }}
                            {% if form.phone.help_text %}
                                <div class="form-text-cw">{{ form.phone.help_text }}</div>
                            {% endif %}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback-cw">{{ form.phone.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Email Address
                            </label>
                            {{ form.email|add_class:"form-control-cw" }}
                            {% if form.email.help_text %}
                                <div class="form-text-cw">{{ form.email.help_text }}</div>
                            {% endif %}
                            {% if form.email.errors %}
                                <div class="invalid-feedback-cw">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Website URL
                    </label>
                    {{ form.website_url|add_class:"form-control-cw" }}
                    {% if form.website_url.help_text %}
                        <div class="form-text-cw">{{ form.website_url.help_text }}</div>
                    {% endif %}
                    {% if form.website_url.errors %}
                        <div class="invalid-feedback-cw">{{ form.website_url.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Additional Information -->
            <div class="form-section-cw">
                <h3 class="section-title-cw">
                    <i class="fas fa-info"></i>
                    Additional Information
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Latitude
                            </label>
                            {{ form.latitude|add_class:"form-control-cw" }}
                            {% if form.latitude.help_text %}
                                <div class="form-text-cw">{{ form.latitude.help_text }}</div>
                            {% endif %}
                            {% if form.latitude.errors %}
                                <div class="invalid-feedback-cw">{{ form.latitude.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-cw">
                            <label class="form-label-cw">
                                Longitude
                            </label>
                            {{ form.longitude|add_class:"form-control-cw" }}
                            {% if form.longitude.help_text %}
                                <div class="form-text-cw">{{ form.longitude.help_text }}</div>
                            {% endif %}
                            {% if form.longitude.errors %}
                                <div class="invalid-feedback-cw">{{ form.longitude.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group-cw">
                    <label class="form-label-cw">
                        Opening Notes
                    </label>
                    {{ form.opening_notes|add_class:"form-control-cw" }}
                    {% if form.opening_notes.help_text %}
                        <div class="form-text-cw">{{ form.opening_notes.help_text }}</div>
                    {% endif %}
                    {% if form.opening_notes.errors %}
                        <div class="invalid-feedback-cw">{{ form.opening_notes.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-save me-2"></i>Update Venue
                </button>
                <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</section>
{% endblock %}
