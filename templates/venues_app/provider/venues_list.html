{% extends 'base.html' %}

{% block title %}My Venues - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venues List */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Venues Dashboard Section */
    .venues-dashboard-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    /* Page Header */
    .dashboard-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .dashboard-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Cards */
    .card-cw {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-brand {
        border: 2px solid var(--cw-brand-primary);
        background: var(--cw-gradient-card);
        box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
    }

    .card-cw-accent {
        border: 1px solid var(--cw-brand-accent);
        background: var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-sm);
    }

    /* Quick Actions Card */
    .quick-actions-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
    }

    .quick-actions-title {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    /* Stats Cards */
    .stat-card {
        text-align: center;
        padding: 2rem 1rem;
        background: white;
        border-radius: 1rem;
        border: 1px solid var(--cw-neutral-200);
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .stat-card i {
        font-size: 2.5rem;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-weight: 500;
        font-size: 0.95rem;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        text-decoration: none;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-accent {
        background: var(--cw-gradient-accent);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
        text-decoration: none;
    }

    .btn-cw-accent:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .venues-dashboard-section {
            padding: 2rem 0;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stat-card {
            padding: 1.5rem 1rem;
        }

        .stat-card i {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venues-dashboard-section">
    <div class="container">
        <!-- Page Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">My Venues</h1>
            <p class="dashboard-subtitle">Manage your venues and services on CozyWish</p>
        </div>

        <!-- Quick Navigation -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-cw quick-actions-card">
                    <div class="card-body p-4">
                        <h6 class="quick-actions-title">
                            <i class="fas fa-tachometer-alt me-2"></i>Quick Actions
                        </h6>
                        <div class="d-flex flex-wrap gap-3">
                            {% if not venues %}
                            <a href="{% url 'venues_app:venue_create' %}" class="btn btn-cw-primary">
                                <i class="fas fa-plus me-2"></i>Create Your Venue
                            </a>
                            {% endif %}
                            <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-percentage me-2"></i>Manage Discounts
                            </a>
                            <a href="{% url 'discount_app:create_service_discount' %}" class="btn btn-cw-accent">
                                <i class="fas fa-tag me-2"></i>Service Discount
                            </a>
                            <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-cw-accent">
                                <i class="fas fa-tags me-2"></i>Venue Discount
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Stats -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="stat-card h-100">
                    <i class="fas fa-store"></i>
                    <div class="stat-number">{{ venues.count }}</div>
                    <div class="stat-label">Total Venues</div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card h-100">
                    <i class="fas fa-spa"></i>
                    <div class="stat-number">{{ total_services|default:"0" }}</div>
                    <div class="stat-label">Total Services</div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card h-100">
                    <i class="fas fa-calendar-check"></i>
                    <div class="stat-number">{{ total_bookings|default:"0" }}</div>
                    <div class="stat-label">Total Bookings</div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card h-100">
                    <i class="fas fa-star"></i>
                    <div class="stat-number">{{ avg_rating|default:"0.0" }}</div>
                    <div class="stat-label">Avg. Rating</div>
                </div>
            </div>
        </div>

        <!-- Venues List -->
        <div id="venues-skeleton" class="row" aria-hidden="true">
            {% for i in "123"|make_list %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card-cw h-100 placeholder-glow">
                    <div class="ratio ratio-16x9">
                        <span class="placeholder w-100 h-100" style="background: var(--cw-neutral-200);"></span>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="card-title"><span class="placeholder col-6" style="background: var(--cw-neutral-200);"></span></h5>
                        <p class="card-text"><span class="placeholder col-7" style="background: var(--cw-neutral-200);"></span></p>
                        <p class="card-text"><span class="placeholder col-4" style="background: var(--cw-neutral-200);"></span></p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div id="venues-list" class="row d-none">
            {% if venues %}
                {% for venue in venues %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-cw h-100">
                        <div class="position-relative">
                            <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.name }}" style="height: 200px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge {% if venue.approval_status == 'approved' %}badge-cw-success{% elif venue.approval_status == 'pending' %}badge-cw-warning{% else %}badge-cw-danger{% endif %}">
                                    {{ venue.get_approval_status_display }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="venue-card-title">{{ venue.name }}</h5>
                            <div class="mb-3">
                                <span class="rating-stars">{{ venue.get_average_rating }}★</span>
                                <span class="rating-count">({{ venue.get_review_count }})</span>
                            </div>
                            <p class="venue-location mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ venue.city }}, {{ venue.state }}
                            </p>
                            <p class="venue-services mb-3">
                                <i class="fas fa-spa me-2"></i>{{ venue.services.count }} services
                            </p>

                            {% if venue.approval_status == 'rejected' %}
                            <div class="alert-cw-error mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Rejection Reason:</strong> {{ venue.rejection_reason }}
                            </div>
                            {% endif %}

                            <div class="d-flex flex-column gap-2">
                                <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-cw-primary">
                                    <i class="fas fa-edit me-2"></i>Manage Venue
                                </a>
                                {% if venue.approval_status == 'approved' %}
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-cw-secondary">
                                    <i class="fas fa-eye me-2"></i>Public View
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="card-cw-accent text-center">
                        <div class="card-body p-5">
                            <i class="fas fa-store fa-4x text-brand-cw mb-4"></i>
                            <h4 class="text-brand-cw mb-3">No Venues Yet</h4>
                            <p class="text-neutral-cw mb-4">You don't have any venues yet. Create your first venue to get started on CozyWish!</p>
                            <a href="{% url 'venues_app:venue_create' %}" class="btn btn-cw-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Venue
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

    {% if page_obj.has_other_pages %}
    <nav aria-label="Venue pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
            {% endif %}
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                {% endif %}
            {% endfor %}
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
{% endblock %}

{% block venues_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function(){
  const skeleton = document.getElementById('venues-skeleton');
  const list = document.getElementById('venues-list');
  if(list){
    list.classList.remove('d-none');
  }
  if(skeleton){
    skeleton.remove();
  }
});
</script>
{% endblock %}
