{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Flagged Venues - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="display-font text-brand-cw mb-2">Flagged Venues</h1>
                    <p class="lead text-neutral-cw mb-0">Review and manage venues that have been flagged by users</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-secondary">
                        <i class="fas fa-list me-2"></i>All Venues
                    </a>
                    <a href="{% url 'venues_app:admin_approval_dashboard' %}" class="btn btn-cw-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flagged Venues List -->
    <div class="row">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-header bg-light-cw border-0">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0 text-brand-cw fw-bold">
                            <i class="fas fa-flag me-2"></i>Flagged Content
                        </h5>
                        {% if flagged_venues %}
                            <span class="badge bg-danger">{{ flagged_venues|length }} flagged</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if flagged_venues %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light-cw">
                                    <tr>
                                        <th class="text-brand-cw fw-bold border-0 py-3 px-4">Venue Details</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Reporter</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Reason</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Status</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3">Date</th>
                                        <th class="text-brand-cw fw-bold border-0 py-3 px-4">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for flag in flagged_venues %}
                                    <tr class="border-light">
                                        <td class="py-3 px-4">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-building text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold text-brand-cw">{{ flag.venue.name }}</div>
                                                    <small class="text-neutral-cw">
                                                        <i class="fas fa-user me-1"></i>{{ flag.venue.owner.email }}
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                    <i class="fas fa-user text-brand-cw"></i>
                                                </div>
                                                <span class="text-neutral-cw">{{ flag.flagged_by.get_full_name|default:flag.flagged_by.email }}</span>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            <div class="text-neutral-cw">
                                                {{ flag.reason|truncatechars:80 }}
                                                {% if flag.reason|length > 80 %}
                                                    <button class="btn btn-link btn-sm p-0 text-brand-cw"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ flag.reason }}">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            {% if flag.status == 'pending' %}
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-clock me-1"></i>Pending Review
                                                </span>
                                            {% elif flag.status == 'reviewed' %}
                                                <span class="badge bg-info">
                                                    <i class="fas fa-eye me-1"></i>Under Review
                                                </span>
                                            {% elif flag.status == 'resolved' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Resolved
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="py-3">
                                            <div class="text-neutral-cw">
                                                <div>{{ flag.created_at|date:"M d, Y" }}</div>
                                                <small class="text-muted">{{ flag.created_at|date:"H:i" }}</small>
                                            </div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="d-flex gap-1">
                                                <a href="{% url 'venues_app:admin_venue_detail' venue_id=flag.venue.id %}"
                                                   class="btn btn-cw-accent-outline btn-sm"
                                                   title="View Venue Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if flag.status == 'pending' %}
                                                    <button class="btn btn-cw-primary btn-sm"
                                                            title="Mark as Reviewed"
                                                            onclick="updateFlagStatus({{ flag.id }}, 'reviewed')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                {% endif %}
                                                {% if flag.status != 'resolved' %}
                                                    <button class="btn btn-success btn-sm"
                                                            title="Mark as Resolved"
                                                            onclick="updateFlagStatus({{ flag.id }}, 'resolved')">
                                                        <i class="fas fa-check-double"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <div class="bg-light-cw rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 80px; height: 80px;">
                                <i class="fas fa-shield-check text-success fa-2x"></i>
                            </div>
                            <h5 class="text-brand-cw mb-2">No Flagged Venues</h5>
                            <p class="text-neutral-cw mb-0">All venues are in good standing. Great job maintaining quality!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block venues_extra_js %}
<script>
function updateFlagStatus(flagId, status) {
    if (confirm('Are you sure you want to update this flag status?')) {
        // Add AJAX call to update flag status
        // This would need to be implemented in the backend
        console.log('Update flag', flagId, 'to status', status);
        // For now, just reload the page
        location.reload();
    }
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
