{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Services - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Manage Services */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Page Section */
    .services-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    .services-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header Section */
    .services-header {
        background: var(--cw-gradient-card-subtle);
        padding: 2rem 2rem 1.5rem;
        border-radius: 1rem 1rem 0 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        position: relative;
        margin-bottom: 2rem;
    }

    .services-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="services-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23services-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
        border-radius: 1rem 1rem 0 0;
    }

    .services-header .content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .services-title {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .services-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin: 0;
        line-height: 1.4;
    }

    /* Back Button */
    .btn-cw-back {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 2rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Main Card */
    .services-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
    }</style>
    /* Service Cards */
    .service-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .service-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .service-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .service-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        line-height: 1.3;
    }

    .service-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .service-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .service-detail-item {
        text-align: center;
        padding: 0.75rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .service-detail-value {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        margin-bottom: 0.25rem;
    }

    .service-detail-label {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin: 0;
    }

    .service-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-disabled {
        border: 2px solid var(--cw-neutral-600);
        color: var(--cw-neutral-600);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Dropdown */
    .service-actions {
        position: relative;
    }

    .dropdown-toggle-cw {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 0.5rem;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .dropdown-toggle-cw:hover {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .dropdown-menu-cw {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 0.5rem;
        min-width: 160px;
    }

    .dropdown-item-cw {
        color: var(--cw-brand-primary);
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .dropdown-item-cw:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    .dropdown-item-cw.danger {
        color: #dc2626;
    }

    .dropdown-item-cw.danger:hover {
        background: #dc2626;
        color: white;
    }

    /* Badge */
    .badge-cw-active {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    .badge-cw-inactive {
        background: var(--cw-neutral-600);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }</style>
{% endblock %}

{% block content %}
<section class="services-section">
    <div class="services-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            Back to Venue Management
        </a>

        <!-- Main Card -->
        <div class="services-card">
            <!-- Header -->
            <div class="services-header">
                <div class="content">
                    <div>
                        <h1 class="services-title">Manage Services</h1>
                        <p class="services-subtitle">{{ venue.venue_name }} - {{ services.count }}/{{ max_services }} services</p>
                    </div>
                    <div>
                        {% if can_add_service %}
                            <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                                <i class="fas fa-plus"></i>Add Service
                            </a>
                        {% else %}
                            <div class="btn-cw-disabled" title="Maximum {{ max_services }} services allowed">
                                <i class="fas fa-plus"></i>Add Service (Limit Reached)
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Services List -->
            <div style="padding: 2rem;">
                {% if services %}
                    <div class="row">
                        {% for service in services %}
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="service-card h-100">
                                    <div class="service-card-header">
                                        <h3 class="service-title">{{ service.service_title }}</h3>
                                        <div class="service-actions">
                                            <div class="dropdown">
                                                <button class="dropdown-toggle-cw" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-cw">
                                                    <li>
                                                        <a class="dropdown-item-cw" href="{% url 'venues_app:service_edit' service.pk %}">
                                                            <i class="fas fa-edit"></i>Edit
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item-cw danger" href="{% url 'venues_app:service_delete' service.pk %}">
                                                            <i class="fas fa-trash"></i>Delete
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <p class="service-description">{{ service.short_description|truncatewords:20 }}</p>

                                    <div class="service-details">
                                        <div class="service-detail-item">
                                            <div class="service-detail-value">{{ service.price_display }}</div>
                                            <p class="service-detail-label">Price</p>
                                        </div>
                                        <div class="service-detail-item">
                                            <div class="service-detail-value">{{ service.duration_display }}</div>
                                            <p class="service-detail-label">Duration</p>
                                        </div>
                                    </div>

                                    <div class="service-status">
                                        {% if service.is_active %}
                                            <span class="badge-cw-active">Active</span>
                                        {% else %}
                                            <span class="badge-cw-inactive">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}

                    <!-- Empty State -->
                    <div style="text-align: center; padding: 3rem 2rem; color: var(--cw-neutral-600);">
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-spa" style="font-size: 3rem; color: var(--cw-brand-accent); background: var(--cw-brand-primary); padding: 1rem; border-radius: 50%;"></i>
                        </div>
                        <h3 style="font-family: var(--cw-font-heading); font-weight: 600; color: var(--cw-brand-primary); margin-bottom: 1rem;">No Services Yet</h3>
                        <p style="font-size: 1.125rem; margin-bottom: 2rem;">Start by adding your first service to attract customers.</p>
                        {% if can_add_service %}
                            <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                                <i class="fas fa-plus"></i>Add Your First Service
                            </a>
                        {% endif %}
                    </div>
                {% endif %}

                <!-- Guidelines Card -->
                <div style="margin-top: 3rem;">
                    <div style="background: var(--cw-accent-light); border: 1px solid var(--cw-brand-accent); border-radius: 1rem; padding: 2rem;">
                        <h3 style="font-family: var(--cw-font-heading); font-weight: 600; color: var(--cw-brand-primary); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-lightbulb" style="color: var(--cw-brand-accent); background: var(--cw-brand-primary); padding: 0.5rem; border-radius: 0.5rem; font-size: 1rem;"></i>
                            Service Management Tips
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Keep service descriptions clear and detailed
                                    </li>
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Set competitive and accurate pricing
                                    </li>
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-check-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Use realistic duration estimates
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Maximum {{ max_services }} services per venue
                                    </li>
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Variable pricing supported (min-max range)
                                    </li>
                                    <li style="margin-bottom: 1rem; display: flex; align-items: center; color: var(--cw-neutral-600);">
                                        <i class="fas fa-info-circle" style="margin-right: 0.75rem; color: var(--cw-brand-primary);"></i>
                                        Inactive services won't be bookable
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Responsive Design */
    @media (max-width: 768px) {
        .services-section {
            padding: 2rem 0;
        }

        .services-container {
            padding: 0 0.5rem;
        }

        .services-header .content {
            flex-direction: column;
            gap: 1rem;
        }

        .services-title {
            font-size: 1.75rem;
        }

        .service-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
