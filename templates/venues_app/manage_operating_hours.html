{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage Operating Hours - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Manage Operating Hours */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Page Section */
    .hours-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    .hours-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header Section */
    .hours-header {
        background: var(--cw-gradient-card-subtle);
        padding: 2rem 2rem 1.5rem;
        border-radius: 1rem 1rem 0 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        position: relative;
    }

    .hours-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hours-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23hours-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
        border-radius: 1rem 1rem 0 0;
    }

    .hours-header .content {
        position: relative;
        z-index: 2;
    }

    .hours-title {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .hours-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin: 0;
        line-height: 1.4;
    }

    /* Main Card */
    .hours-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    /* Back Button */
    .btn-cw-back {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 2rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Operating Hours Form Section */
    .hours-form-section {
        padding: 2rem;
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
    }</style>
    /* Day Row Styles */
    .day-row {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }

    .day-row:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-sm);
    }

    .day-row:last-child {
        margin-bottom: 0;
    }

    .day-name {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        width: 120px;
        flex-shrink: 0;
        margin-bottom: 1rem;
    }

    .time-inputs {
        display: flex;
        align-items: flex-end;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .time-input {
        flex: 1;
        min-width: 140px;
        max-width: 160px;
    }

    .closed-checkbox {
        display: flex;
        align-items: center;
        margin-top: 1.5rem;
    }

    /* Form Styles */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.875rem;
    }

    .form-control {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control:disabled {
        background-color: var(--cw-neutral-100);
        border-color: var(--cw-neutral-300);
        color: var(--cw-neutral-500);
        opacity: 0.6;
    }

    .form-check-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-label {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        font-weight: 500;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--cw-brand-accent);
    }

    /* Error Messages */
    .text-danger {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-family: var(--cw-font-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hours-section {
            padding: 2rem 0;
        }

        .hours-container {
            padding: 0 0.5rem;
        }

        .hours-header,
        .hours-form-section {
            padding: 1.5rem;
        }

        .day-row {
            padding: 1rem;
        }

        .day-name {
            width: 100%;
            margin-bottom: 1rem;
        }

        .time-inputs {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .time-input {
            max-width: none;
        }

        .closed-checkbox {
            margin-top: 1rem;
        }

        .hours-title {
            font-size: 1.75rem;
        }

        .action-buttons {
            flex-direction: column;
        }
    }

    @media (min-width: 769px) {
        .day-row {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
        }

        .day-name {
            margin-bottom: 0;
            margin-top: 1.5rem;
        }

        .time-inputs {
            flex: 1;
        }

        .closed-checkbox {
            margin-top: 0;
            margin-left: 1rem;
        }

        .action-buttons {
            flex-direction: row;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="hours-section">
    <div class="hours-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            {% trans "Back to Venue Management" %}
        </a>

        <!-- Main Card -->
        <div class="hours-card">
            <!-- Header -->
            <div class="hours-header">
                <div class="content">
                    <h1 class="hours-title">{% trans "Manage Operating Hours" %}</h1>
                    <p class="hours-subtitle">Set your venue's operating hours for each day of the week</p>
                </div>
            </div>

            <!-- Operating Hours Form Section -->
            <div class="hours-form-section">
                <h2 class="section-title">
                    <i class="fas fa-clock"></i>
                    Weekly Schedule
                </h2>

                <form method="post" novalidate>
                    {% csrf_token %}
                    {{ formset.management_form }}

                    {% for form, day_name in formset_with_days %}
                        <div class="day-row">
                            <div class="day-name">{{ day_name }}</div>

                            <div class="time-inputs">
                                {{ form.day.as_hidden }}

                                <div class="time-input">
                                    <label class="form-label">Opening Time</label>
                                    {{ form.opening|add_class:"form-control" }}
                                    {% for error in form.opening.errors %}
                                        <div class="text-danger">{{ error }}</div>
                                    {% endfor %}
                                </div>

                                <div class="time-input">
                                    <label class="form-label">Closing Time</label>
                                    {{ form.closing|add_class:"form-control" }}
                                    {% for error in form.closing.errors %}
                                        <div class="text-danger">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="closed-checkbox">
                                <div class="form-check">
                                    {{ form.is_closed|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_closed.id_for_label }}">
                                        Closed
                                    </label>
                                </div>
                            </div>

                            {% for error in form.non_field_errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endfor %}

                    {% for error in formset.non_form_errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-save"></i>
                            Save Operating Hours
                        </button>
                        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle closed checkbox changes
    const closedCheckboxes = document.querySelectorAll('input[name$="-is_closed"]');

    closedCheckboxes.forEach(function(checkbox) {
        const row = checkbox.closest('.day-row');
        const timeInputs = row.querySelectorAll('input[type="time"]');

        function toggleTimeInputs() {
            timeInputs.forEach(function(input) {
                input.disabled = checkbox.checked;
                if (checkbox.checked) {
                    input.value = '';
                }
            });
        }

        // Initial state
        toggleTimeInputs();

        // Handle changes
        checkbox.addEventListener('change', toggleTimeInputs);
    });
});
</script>
{% endblock %}
