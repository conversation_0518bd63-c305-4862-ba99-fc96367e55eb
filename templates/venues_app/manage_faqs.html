{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage FAQs - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Manage FAQs */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Page Section */
    .faqs-section {
        padding: 3rem 0;
        min-height: 100vh;
    }

    .faqs-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header Section */
    .faqs-header {
        background: var(--cw-gradient-card-subtle);
        padding: 2rem 2rem 1.5rem;
        border-radius: 1rem 1rem 0 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        position: relative;
    }

    .faqs-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="faqs-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23faqs-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
        border-radius: 1rem 1rem 0 0;
    }

    .faqs-header .content {
        position: relative;
        z-index: 2;
    }

    .faqs-title {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .faqs-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin: 0;
        line-height: 1.4;
    }

    /* Main Card */
    .faqs-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    /* Back Button */
    .btn-cw-back {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 2rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* FAQs List Section */
    .faqs-list-section {
        padding: 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
    }

    .faq-item {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }

    .faq-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .faq-item:last-child {
        margin-bottom: 0;
    }

    .faq-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .faq-info .faq-question {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        line-height: 1.3;
    }

    .faq-info .faq-answer {
        color: var(--cw-neutral-600);
        margin: 0;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .faq-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }</style>
    /* Empty State */
    .no-faqs-message {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-600);
    }

    .no-faqs-message i {
        font-size: 3rem;
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 1rem;
        border-radius: 50%;
        margin-bottom: 1.5rem;
        display: inline-block;
    }

    .no-faqs-message p {
        font-size: 1.125rem;
        margin: 0;
    }

    /* Add FAQ Form Section */
    .add-faq-section {
        padding: 2rem;
    }

    .form-section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: var(--cw-brand-accent);
        background: var(--cw-brand-primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
    }

    .faq-limit-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .faq-limit-info i {
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
    }

    /* Form Styles */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.95rem;
    }

    .form-control {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Error Messages */
    .text-danger {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        font-family: var(--cw-font-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .faqs-section {
            padding: 2rem 0;
        }

        .faqs-container {
            padding: 0 0.5rem;
        }

        .faqs-header,
        .faqs-list-section,
        .add-faq-section {
            padding: 1.5rem;
        }

        .faq-content {
            flex-direction: column;
            gap: 1rem;
        }

        .faq-actions {
            align-self: flex-start;
        }

        .faqs-title {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="faqs-section">
    <div class="faqs-container">
        <!-- Back Button -->
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn-cw-back">
            <i class="fas fa-arrow-left"></i>
            {% trans "Back to Venue Management" %}
        </a>

        <!-- Main Card -->
        <div class="faqs-card">
            <!-- Header -->
            <div class="faqs-header">
                <div class="content">
                    <h1 class="faqs-title">{% trans "Manage FAQs" %}</h1>
                    <p class="faqs-subtitle">Add and manage frequently asked questions about your venue</p>
                </div>
            </div>

            <!-- Current FAQs Section -->
            <div class="faqs-list-section">
                <h2 class="section-title">
                    <i class="fas fa-question-circle"></i>
                    Current FAQs ({{ faqs.count }}/{{ max_faqs }})
                </h2>

                {% if faqs %}
                    {% for faq in faqs %}
                        <div class="faq-item">
                            <div class="faq-content">
                                <div class="faq-info">
                                    <div class="faq-question">{{ faq.order }}. {{ faq.question }}</div>
                                    <p class="faq-answer">{{ faq.answer }}</p>
                                </div>
                                <div class="faq-actions">
                                    <a href="{% url 'venues_app:edit_faq' faq.id %}" class="btn-cw-secondary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'venues_app:delete_faq' faq.id %}" class="btn-cw-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-faqs-message">
                        <i class="fas fa-question-circle"></i>
                        <p>{% trans "No FAQs yet. Add your first FAQ below." %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Add New FAQ Section -->
            {% if can_add_faq %}
            <div class="add-faq-section">
                <h2 class="form-section-title">
                    <i class="fas fa-plus"></i>
                    {% trans "Add New FAQ" %}
                </h2>

                <form method="post" novalidate>
                    {% csrf_token %}

                    <div class="mb-3">
                        <label class="form-label" for="{{ form.question.id_for_label }}">{% trans "Question" %}</label>
                        {{ form.question|add_class:'form-control' }}
                        {% for error in form.question.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-4">
                        <label class="form-label" for="{{ form.answer.id_for_label }}">{% trans "Answer" %}</label>
                        {{ form.answer|add_class:'form-control' }}
                        {% for error in form.answer.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <button type="submit" class="btn-cw-primary">
                        <i class="fas fa-plus"></i>
                        {% trans "Add FAQ" %}
                    </button>
                </form>
            </div>
            {% else %}
            <div class="add-faq-section">
                <div class="faq-limit-info">
                    <i class="fas fa-exclamation-triangle"></i>
                    {% blocktrans with max=max_faqs %}Maximum {{ max }} FAQs reached.{% endblocktrans %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
