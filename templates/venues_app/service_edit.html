{% extends 'base.html' %}

{% block title %}Edit Service - {{ object.service_title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Service Edit Section */
    .service-edit-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* Header */
    .service-header {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .service-header h2 {
        font-family: var(--cw-font-heading);
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
    }

    .service-header p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-size: 1rem;
    }

    .back-button-cw {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-family: var(--cw-font-primary);
    }

    .back-button-cw:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Cards */
    .card-cw {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
        position: relative;
    }

    .card-cw-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="card-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23card-pattern)"/></svg>') repeat;
        opacity: 0.4;
        z-index: 1;
    }

    .card-cw-header .content {
        position: relative;
        z-index: 2;
    }

    .card-cw-header h5 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-cw-body {
        padding: 2rem;
    }

    /* Form Styling */
    .form-label-cw {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-family: var(--cw-font-primary);
        font-size: 1rem;
        background: white;
        color: var(--cw-neutral-800);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
    }

    .required-cw {
        color: #dc2626;
        font-weight: 600;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .invalid-feedback-cw {
        color: #dc2626;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
        display: block;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 2rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        text-decoration: none;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Checkbox Styling */
    .form-check-cw {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .form-check-input-cw {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.25rem;
        background: white;
        cursor: pointer;
    }

    .form-check-input-cw:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label-cw {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 600;
        cursor: pointer;
    }

    /* Alert Styling */
    .alert-cw-danger {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    /* Badge Styling */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    .badge-cw-secondary {
        background: var(--cw-neutral-600);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    /* Action Buttons */
    .action-buttons-cw {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    @media (min-width: 768px) {
        .action-buttons-cw {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="service-edit-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="service-header">
                    <a href="{% url 'venues_app:manage_services' %}" class="back-button-cw">
                        <i class="fas fa-arrow-left"></i>
                        Back
                    </a>
                    <div>
                        <h2>Edit Service</h2>
                        <p>Update service details for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Service Form -->
                <div class="card-cw">
                    <div class="card-cw-body">
                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Service Title -->
                            <div class="mb-4">
                                <label for="{{ form.service_title.id_for_label }}" class="form-label-cw">
                                    Service Name <span class="required-cw">*</span>
                                </label>
                                {% if form.service_title.errors %}
                                    {{ form.service_title|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.service_title.errors.0 }}</div>
                                {% else %}
                                    {{ form.service_title|add_class:"form-control-cw" }}
                                {% endif %}
                                {% if form.service_title.help_text %}
                                    <div class="form-text-cw">{{ form.service_title.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Service Description -->
                            <div class="mb-4">
                                <label for="{{ form.short_description.id_for_label }}" class="form-label-cw">
                                    Service Description <span class="required-cw">*</span>
                                </label>
                                {% if form.short_description.errors %}
                                    {{ form.short_description|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.short_description.errors.0 }}</div>
                                {% else %}
                                    {{ form.short_description|add_class:"form-control-cw" }}
                                {% endif %}
                                {% if form.short_description.help_text %}
                                    <div class="form-text-cw">{{ form.short_description.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Pricing Section -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="{{ form.price_min.id_for_label }}" class="form-label-cw">
                                            Minimum Price ($) <span class="required-cw">*</span>
                                        </label>
                                        {% if form.price_min.errors %}
                                            {{ form.price_min|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.price_min.errors.0 }}</div>
                                        {% else %}
                                            {{ form.price_min|add_class:"form-control-cw" }}
                                        {% endif %}
                                        {% if form.price_min.help_text %}
                                            <div class="form-text-cw">{{ form.price_min.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="{{ form.price_max.id_for_label }}" class="form-label-cw">
                                            Maximum Price ($)
                                        </label>
                                        {% if form.price_max.errors %}
                                            {{ form.price_max|add_class:"form-control-cw is-invalid" }}
                                            <div class="invalid-feedback-cw">{{ form.price_max.errors.0 }}</div>
                                        {% else %}
                                            {{ form.price_max|add_class:"form-control-cw" }}
                                        {% endif %}
                                        {% if form.price_max.help_text %}
                                            <div class="form-text-cw">{{ form.price_max.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Duration -->
                            <div class="mb-4">
                                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label-cw">
                                    Duration (minutes) <span class="required-cw">*</span>
                                </label>
                                {% if form.duration_minutes.errors %}
                                    {{ form.duration_minutes|add_class:"form-control-cw is-invalid" }}
                                    <div class="invalid-feedback-cw">{{ form.duration_minutes.errors.0 }}</div>
                                {% else %}
                                    {{ form.duration_minutes|add_class:"form-control-cw" }}
                                {% endif %}
                                {% if form.duration_minutes.help_text %}
                                    <div class="form-text-cw">{{ form.duration_minutes.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Service Availability Toggle -->
                            <div class="mb-4">
                                <div class="form-check-cw">
                                    {{ form.is_active|add_class:"form-check-input-cw" }}
                                    <label class="form-check-label-cw" for="{{ form.is_active.id_for_label }}">
                                        Service Available to Customers
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="invalid-feedback-cw">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                                {% if form.is_active.help_text %}
                                    <div class="form-text-cw mt-2">{{ form.is_active.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="alert-cw-danger">
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Submit Buttons -->
                            <div class="action-buttons-cw">
                                <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn-cw-primary">
                                    <i class="fas fa-save me-2"></i>Update Service
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Current Service Info -->
                <div class="card-cw mb-4">
                    <div class="card-cw-header">
                        <div class="content">
                            <h5>
                                <i class="fas fa-spa"></i>
                                Current Service
                            </h5>
                        </div>
                    </div>
                    <div class="card-cw-body">
                        <h6 class="mb-2" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">{{ object.service_title }}</h6>
                        <p class="mb-3" style="color: var(--cw-neutral-600); font-size: 0.9rem;">{{ object.short_description|truncatewords:15 }}</p>

                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div style="border-right: 1px solid var(--cw-brand-accent); padding-right: 1rem;">
                                    <div class="fw-bold" style="color: var(--cw-brand-primary);">{{ object.price_display }}</div>
                                    <small style="color: var(--cw-neutral-600);">Current Price</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold" style="color: var(--cw-brand-primary);">{{ object.duration_display }}</div>
                                <small style="color: var(--cw-neutral-600);">Current Duration</small>
                            </div>
                        </div>

                        <div class="text-center">
                            {% if object.is_active %}
                                <span class="badge-cw-primary">Active</span>
                            {% else %}
                                <span class="badge-cw-secondary">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Guidelines -->
                <div class="card-cw mb-4">
                    <div class="card-cw-header">
                        <div class="content">
                            <h5>
                                <i class="fas fa-lightbulb"></i>
                                Update Guidelines
                            </h5>
                        </div>
                    </div>
                    <div class="card-cw-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-3 d-flex align-items-start">
                                <i class="fas fa-check-circle me-2 mt-1" style="color: var(--cw-brand-primary);"></i>
                                <span style="color: var(--cw-neutral-700);">Review pricing against competitors</span>
                            </li>
                            <li class="mb-3 d-flex align-items-start">
                                <i class="fas fa-check-circle me-2 mt-1" style="color: var(--cw-brand-primary);"></i>
                                <span style="color: var(--cw-neutral-700);">Update descriptions based on feedback</span>
                            </li>
                            <li class="mb-3 d-flex align-items-start">
                                <i class="fas fa-check-circle me-2 mt-1" style="color: var(--cw-brand-primary);"></i>
                                <span style="color: var(--cw-neutral-700);">Adjust duration for accuracy</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="fas fa-exclamation-triangle me-2 mt-1" style="color: #d97706;"></i>
                                <span style="color: var(--cw-neutral-700);">Changes may affect existing bookings</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Venue Info -->
                <div class="card-cw">
                    <div class="card-cw-header">
                        <div class="content">
                            <h5>
                                <i class="fas fa-building"></i>
                                Venue Information
                            </h5>
                        </div>
                    </div>
                    <div class="card-cw-body">
                        <h6 class="mb-2" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;">{{ venue.venue_name }}</h6>
                        <p class="mb-3" style="color: var(--cw-neutral-600);">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ venue.city }}, {{ venue.state }}
                        </p>
                        <p class="mb-3" style="color: var(--cw-neutral-600);">
                            <i class="fas fa-spa me-1"></i>
                            {{ venue.services.count }}/7 services
                        </p>
                        <a href="{% url 'venues_app:venue_edit' %}" class="btn-cw-secondary w-100 text-center d-block">
                            <i class="fas fa-edit me-2"></i>Edit Venue
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
