{% extends 'base.html' %}

{% block title %}Add Service - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Create */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Service wrapper */
    .service-wrapper {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--cw-font-primary);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-accent {
        border: 1px solid var(--cw-brand-accent);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-sm);
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
    }
    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Custom Forms */
    .form-control-cw {
        border: 2px solid rgba(250, 225, 215, 0.5);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
    }
    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
        color: var(--cw-neutral-800);
    }

    /* Form Labels */
    .form-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    /* Text Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
    .text-danger { color: var(--cw-brand-primary) !important; font-weight: 600; }

    /* Background Colors */
    .bg-light-cw { background-color: var(--cw-accent-light) !important; }

    /* Header styling */
    .service-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .service-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--cw-brand-primary);
    }

    .service-header p {
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Checkbox styling */
    .form-check-input {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        background-color: white;
        width: 1.2em;
        height: 1.2em;
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-primary);
        font-weight: 500;
        margin-left: 0.25rem;
    }

    /* Invalid feedback */
    .invalid-feedback {
        color: var(--cw-brand-primary);
        font-weight: 500;
        margin-top: 0.5rem;
    }

    /* Alert styling */
    .alert-danger {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="service-wrapper">
    <div class="container py-4">
        <div class="row">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="service-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Add New Service</h2>
                        <p>Create a new service for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Service Form -->
                <div class="card-cw">
                    <div class="card-body p-4">
                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Service Title -->
                            <div class="mb-3">
                                <label for="{{ form.service_title.id_for_label }}" class="form-label">
                                    Service Name <span class="text-danger">*</span>
                                </label>
                                {{ form.service_title|add_class:"form-control form-control-cw" }}
                                {% if form.service_title.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_title.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.service_title.help_text %}
                                    <small class="form-text text-neutral-cw">{{ form.service_title.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Service Description -->
                            <div class="mb-3">
                                <label for="{{ form.short_description.id_for_label }}" class="form-label">
                                    Service Description <span class="text-danger">*</span>
                                </label>
                                {{ form.short_description|add_class:"form-control form-control-cw" }}
                                {% if form.short_description.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.short_description.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.short_description.help_text %}
                                    <small class="form-text text-neutral-cw">{{ form.short_description.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Pricing Section -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.price_min.id_for_label }}" class="form-label">
                                            Minimum Price ($) <span class="text-danger">*</span>
                                        </label>
                                        {{ form.price_min|add_class:"form-control form-control-cw" }}
                                        {% if form.price_min.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.price_min.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.price_min.help_text %}
                                            <small class="form-text text-neutral-cw">{{ form.price_min.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.price_max.id_for_label }}" class="form-label">
                                            Maximum Price ($)
                                        </label>
                                        {{ form.price_max|add_class:"form-control form-control-cw" }}
                                        {% if form.price_max.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.price_max.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.price_max.help_text %}
                                            <small class="form-text text-neutral-cw">{{ form.price_max.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Duration -->
                            <div class="mb-3">
                                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                    Duration (minutes) <span class="text-danger">*</span>
                                </label>
                                {{ form.duration_minutes|add_class:"form-control form-control-cw" }}
                                {% if form.duration_minutes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.duration_minutes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.duration_minutes.help_text %}
                                    <small class="form-text text-neutral-cw">{{ form.duration_minutes.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Service Availability Toggle -->
                            <div class="mb-4">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <strong>Service Available to Customers</strong>
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.is_active.help_text %}
                                        <small class="form-text text-neutral-cw d-block mt-1">{{ form.is_active.help_text }}</small>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Submit Buttons -->
                            <div class="d-flex flex-column gap-3">
                                <button type="submit" class="btn-cw-primary">
                                    <i class="fas fa-plus me-2"></i>Create Service
                                </button>
                                <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">

                <!-- Guidelines -->
                <div class="card-cw-accent mb-4">
                    <div class="card-body p-4">
                        <h5 class="mb-3 text-brand-cw">
                            <i class="fas fa-lightbulb me-2"></i>Service Guidelines
                        </h5>

                        <h6 class="mb-3 text-brand-cw">Service Information</h6>
                        <ul class="list-unstyled mb-4">
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-check-circle me-2 text-brand-cw"></i>
                                Use clear, descriptive service names
                            </li>
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-check-circle me-2 text-brand-cw"></i>
                                Provide detailed service descriptions
                            </li>
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-check-circle me-2 text-brand-cw"></i>
                                Set accurate pricing and duration
                            </li>
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-check-circle me-2 text-brand-cw"></i>
                                Use variable pricing for flexible rates
                            </li>
                        </ul>

                        <h6 class="mb-3 text-brand-cw">Pricing Tips</h6>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-info-circle me-2 text-brand-cw"></i>
                                Leave max price blank for fixed pricing
                            </li>
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-info-circle me-2 text-brand-cw"></i>
                                Use price ranges for customizable services
                            </li>
                            <li class="mb-2 text-neutral-cw">
                                <i class="fas fa-info-circle me-2 text-brand-cw"></i>
                                Consider market rates in your area
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Venue Info -->
                <div class="card-cw">
                    <div class="card-body p-4">
                        <h5 class="mb-3 text-brand-cw">
                            <i class="fas fa-building me-2"></i>Venue Information
                        </h5>

                        <h6 class="mb-2 text-brand-cw">{{ venue.venue_name }}</h6>
                        <p class="text-neutral-cw mb-3">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ venue.city }}, {{ venue.state }}
                        </p>
                        <p class="text-neutral-cw mb-3">
                            <i class="fas fa-spa me-1"></i>
                            {{ venue.services.count }}/7 services
                        </p>
                        <a href="{% url 'venues_app:venue_edit' %}" class="btn-cw-secondary btn-sm w-100">
                            <i class="fas fa-edit me-2"></i>Edit Venue
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
