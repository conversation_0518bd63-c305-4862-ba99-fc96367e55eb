{% extends 'base.html' %}

{% block title %}{{ service.service_title }} - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

<style>
    /* CozyWish Design System - Service Detail */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Service wrapper */
    .service-wrapper {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--cw-font-primary);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-accent {
        border: 1px solid var(--cw-brand-accent);
        background: var(--cw-gradient-card-subtle);
        box-shadow: var(--cw-shadow-sm);
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        letter-spacing: 0.025em;
    }
    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        letter-spacing: 0.025em;
    }
    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Text Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }

    /* Background Colors */
    .bg-light-cw { background-color: var(--cw-accent-light) !important; }

    /* Breadcrumb styling */
    .breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Badge styling */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    /* Rating styling */
    .rating-score {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Back to top button */
    .back-to-top {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-md);
    }

    .back-to-top.show {
        display: flex;
    }

    .back-to-top:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    /* Opening hours styling */
    .opening-hours-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .opening-hours-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .opening-hours-item:last-child {
        border-bottom: none;
    }

    .opening-hours-day {
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    .opening-hours-time {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .opening-hours-closed {
        color: var(--cw-neutral-600);
        font-style: italic;
    }

    /* Service title styling */
    .service-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    /* Venue name styling */
    .venue-name {
        color: var(--cw-neutral-600);
        font-weight: 500;
        font-size: 1.125rem;
    }

    /* Price styling */
    .price-display {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
    }

    .price-original {
        text-decoration: line-through;
        color: var(--cw-neutral-600);
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="service-wrapper">
    <div class="container">
        <div class="row">
            <!-- Service Details -->
            <div class="col-lg-8">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">{{ venue.venue_name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ service.service_title }}</li>
                    </ol>
                </nav>

                <!-- Service Header -->
                <div class="mb-5">
                    <h1 class="service-title">{{ service.service_title }}</h1>
                    <div class="d-flex align-items-center mb-3">
                        <div class="rating me-4">
                            <span class="rating-score">{{ venue.get_average_rating }}★</span>
                            <span class="review-count">({{ venue.get_review_count }} reviews)</span>
                        </div>
                        <span class="venue-name">{{ venue.venue_name }}</span>
                    </div>
                </div>

                <!-- Service Details -->
                <div class="card-cw mb-4">
                    <div class="card-header bg-light-cw border-0 p-4">
                        <h5 class="mb-0 text-brand-cw">Service Details</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <h6 class="text-brand-cw">Price</h6>
                                <div>
                                    {% if service.discounted_price %}
                                    <span class="price-original">${{ service.price_min }}</span>
                                    <span class="price-display">${{ service.discounted_price }}</span>
                                    <span class="badge-cw-primary ms-2">{{ service.get_discount_percentage }}% OFF</span>
                                    {% else %}
                                    <span class="price-display">{{ service.price_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-brand-cw">Duration</h6>
                                <p class="text-neutral-cw mb-0">{{ service.duration_display }}</p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-brand-cw">Category</h6>
                                <p class="text-neutral-cw mb-0">{% for category in venue.categories.all %}{{ category.category_name }}{% if not forloop.last %}, {% endif %}{% empty %}Spa & Wellness{% endfor %}</p>
                            </div>
                        </div>
                        <h6 class="text-brand-cw">Description</h6>
                        <p class="text-neutral-cw">{{ service.short_description }}</p>
                    </div>
                </div>

                <!-- Booking Button -->
                <div class="text-center mb-5">
                    {% if user.is_authenticated and user.is_customer %}
                    <a href="{% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn-cw-primary">
                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                    </a>
                    {% else %}
                    <a href="{% url 'accounts_app:customer_login' %}?next={% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn-cw-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Book
                    </a>
                    {% endif %}
                </div>

                <!-- Venue Information -->
                <div class="card-cw mb-4">
                    <div class="card-header bg-light-cw border-0 p-4">
                        <h5 class="mb-0 text-brand-cw">About {{ venue.venue_name }}</h5>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-neutral-cw">{{ venue.about_venue }}</p>
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn-cw-secondary">
                            <i class="fas fa-store me-2"></i>View Venue Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Opening Hours -->
                <div class="card-cw mb-4">
                    <div class="card-header bg-light-cw border-0 p-4">
                        <h5 class="mb-0 text-brand-cw">Opening Hours</h5>
                    </div>
                    <div class="card-body p-4">
                        <ul class="opening-hours-list">
                            {% for hour in opening_hours %}
                            <li class="opening-hours-item">
                                <span class="opening-hours-day">{{ hour.get_day_display }}</span>
                                {% if hour.is_closed %}
                                <span class="opening-hours-closed">Closed</span>
                                {% else %}
                                <span class="opening-hours-time">{{ hour.opening|time:"g:i A" }} - {{ hour.closing|time:"g:i A" }}</span>
                                {% endif %}
                            </li>
                            {% empty %}
                            <li class="opening-hours-item">
                                <span class="text-neutral-cw">No opening hours available</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <!-- Location Map -->
                <div class="card-cw mb-4">
                    <div class="card-header bg-light-cw border-0 p-4">
                        <h5 class="mb-0 text-brand-cw">Location</h5>
                    </div>
                    <div class="card-body p-4">
                        <p class="mb-3 text-neutral-cw">{{ venue.get_full_address }}</p>
                        <div id="venue-map" style="height: 300px; border: 2px solid var(--cw-brand-accent); border-radius: 0.5rem;" class="mb-3"></div>
                        <a href="https://www.google.com/maps/dir/?api=1&destination={{ venue.latitude }},{{ venue.longitude }}" target="_blank" class="btn-cw-secondary w-100">
                            <i class="fas fa-directions me-2"></i>Get Directions
                        </a>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card-cw-accent mb-4">
                    <div class="card-body p-4">
                        <h5 class="mb-3 text-brand-cw">Contact</h5>
                        <p class="mb-2 text-neutral-cw">
                            <i class="fas fa-envelope me-2 text-brand-cw"></i>
                            {{ venue.service_provider.user.email }}
                        </p>
                        {% if venue.service_provider.phone_number %}
                        <p class="mb-0 text-neutral-cw">
                            <i class="fas fa-phone me-2 text-brand-cw"></i>
                            {{ venue.service_provider.phone_number }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<a href="#" id="back-to-top" class="back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</a>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map if coordinates are available
        {% if venue.latitude and venue.longitude %}
        const venueMap = L.map('venue-map').setView([{{ venue.latitude }}, {{ venue.longitude }}], 15);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(venueMap);

        // Add marker for venue
        L.marker([{{ venue.latitude }}, {{ venue.longitude }}])
            .addTo(venueMap)
            .bindPopup('{{ venue.venue_name }}')
            .openPopup();
        {% endif %}
        const backBtn = document.getElementById('back-to-top');
        window.addEventListener('scroll', function(){
            if(window.scrollY > 200){
                backBtn.classList.add('show');
            } else {
                backBtn.classList.remove('show');
            }
        });
        backBtn.addEventListener('click', function(e){
            e.preventDefault();
            window.scrollTo({top:0, behavior:'smooth'});
        });
    });
</script>
{% endblock %}
