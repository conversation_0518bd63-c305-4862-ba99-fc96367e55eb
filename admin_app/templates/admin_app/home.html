{% extends 'base.html' %}

{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Home Page - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Secondary Colors */
        --cw-secondary-50: #f9f7f4;
        --cw-secondary-100: #f1ebe2;
        --cw-secondary-200: #e3d5c4;
        --cw-secondary-300: #d1b89e;
        --cw-secondary-400: #bc9876;
        --cw-secondary-500: #ad7f5a;
        --cw-secondary-600: #a0704e;
        --cw-secondary-700: #855a42;
        --cw-secondary-800: #6c4a39;
        --cw-secondary-900: #583d30;
        --cw-secondary-950: #2f1f18;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;
        --cw-neutral-950: #0a0a0a;

        /* Semantic Colors */
        --cw-success: #059669;
        --cw-warning: #d97706;
        --cw-error: #dc2626;
        --cw-info: #0284c7;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-hero-alt: radial-gradient(ellipse at center, var(--cw-accent-light) 0%, var(--cw-accent-dark) 40%, #ffffff 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-secondary-950);
        line-height: 1.3;
    }

    .display-font {
        font-family: var(--cw-font-display);
    }

    /* Brand Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-brand-light-cw { color: var(--cw-brand-light) !important; }
    .text-accent-cw { color: var(--cw-brand-accent) !important; }
    .text-secondary-cw { color: var(--cw-secondary-950) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
    .bg-brand-cw { background-color: var(--cw-brand-primary) !important; }
    .bg-brand-accent-cw { background-color: var(--cw-brand-accent) !important; }
    .bg-accent-dark-cw { background-color: var(--cw-accent-dark) !important; }
    .bg-light-cw { background-color: var(--cw-accent-light) !important; }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-accent {
        background: var(--cw-gradient-accent);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
    }
    .btn-cw-accent:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-featured {
        border: 2px solid var(--cw-brand-primary);
        background: var(--cw-gradient-card-subtle);
    }

    .card-cw-brand {
        border: 2px solid var(--cw-brand-primary);
        background: var(--cw-gradient-card);
        box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
    }

    .card-cw-accent {
        border: 1px solid var(--cw-brand-accent);
        background: var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-sm);
    }

    /* Hero Section */
    .hero-section {
        background: var(--cw-gradient-hero);
        padding: 6rem 0 4rem;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="spa-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23spa-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .hero-section .container {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-family: var(--cw-font-display);
        font-size: 4rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(47, 22, 15, 0.1);
    }

    .hero-subtitle {
        font-size: 1.375rem;
        color: var(--cw-neutral-700);
        margin-bottom: 3rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Search Form */
    .search-container {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 1rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .search-input {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
    }

    .search-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .input-icon-left {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--cw-neutral-500);
        z-index: 3;
    }

    .search-divider {
        width: 1px;
        height: 2rem;
        background: var(--cw-neutral-200);
        margin: 0 1rem;
    }

    .search-btn {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        color: white;
        font-weight: 600;
        padding: 1rem 2rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
    }

    .search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }

    .booking-count {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .booking-count strong {
        color: var(--cw-brand-primary);
        font-weight: 700;
    }

    /* Dropdown Menus */
    .dropdown-menu {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        box-shadow: var(--cw-shadow-lg);
        padding: 0.5rem 0;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        color: var(--cw-neutral-700);
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .dropdown-item i {
        margin-right: 0.5rem;
        color: var(--cw-brand-primary);
    }

    .dropdown-header {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Section Styling */
    .section-showcase {
        padding: 5rem 0;
        margin: 0;
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-secondary-950);
        margin-bottom: 1rem;
        text-align: center;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: var(--cw-gradient-brand-button);
        border-radius: 2px;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        text-align: center;
        margin-bottom: 3rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Popular Categories Section */
    .popular-categories {
        padding: 5rem 0;
        background: white;
    }

    .category-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        padding: 2.5rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        background: var(--cw-gradient-card);
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .category-name {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .category-count {
        font-size: 0.95rem;
        color: var(--cw-neutral-600);
    }

    /* Service Highlights Section */
    .service-highlights {
        padding: 5rem 0;
        background: var(--cw-brand-accent);
        position: relative;
    }

    .service-highlights::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="service-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 Q20,10 15,15 Q10,10 15,5" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23service-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .service-highlights .container {
        position: relative;
        z-index: 2;
    }

    .service-card-highlight {
        background: white;
        border-radius: 1rem;
        padding: 2.5rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
        border: 1px solid rgba(250, 225, 215, 0.3);
        text-decoration: none;
        color: inherit;
        box-shadow: var(--cw-shadow-md);
    }

    .service-card-highlight:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        color: inherit;
        text-decoration: none;
        border-color: var(--cw-brand-primary);
    }

    .service-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-accent);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-brand-primary);
        font-size: 2rem;
        margin: 0 auto 1.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .service-name {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .service-description {
        color: var(--cw-neutral-600);
        line-height: 1.6;
        font-size: 1rem;
    }

    /* Venue Cards */
    .venue-sections {
        padding: 5rem 0;
        background: white;
    }

    .venue-sections:nth-child(even) {
        background: var(--cw-secondary-50);
    }

    .venue-card {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .venue-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .venue-card img {
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .venue-card:hover img {
        transform: scale(1.05);
    }

    .venue-card .card-body {
        padding: 1.5rem;
    }

    .venue-card .card-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
    }

    .venue-card .card-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .venue-card .card-title a:hover {
        color: var(--cw-brand-light);
    }

    .rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .rating-score {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
    }

    .location {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .location::before {
        content: '\f3c5';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 0.5rem;
        color: var(--cw-brand-primary);
    }

    .business-type {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        display: inline-block;
    }

    /* Business Section */
    .business-section {
        padding: 5rem 0;
        background: var(--cw-brand-accent);
        position: relative;
    }

    .business-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="business-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 Q20,10 15,15 Q10,10 15,5" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23business-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .business-section .container {
        position: relative;
        z-index: 2;
    }

    .business-title {
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .business-description {
        font-size: 1.25rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .rating-container {
        margin-top: 2rem;
    }

    .rating-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .star-rating {
        margin-bottom: 0.5rem;
    }

    .star-rating .star {
        color: #FFD700;
        font-size: 1.25rem;
        margin-right: 0.25rem;
    }

    .review-text {
        font-size: 1rem;
    }

    .business-preview img {
        max-width: 100%;
        height: auto;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.125rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .search-container {
            padding: 0.75rem;
        }

        .search-input {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .search-btn {
            padding: 0.875rem 1.5rem;
            font-size: 0.95rem;
        }

        .category-icon,
        .service-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .hero-section,
        .popular-categories,
        .service-highlights,
        .venue-sections {
            padding: 3rem 0;
        }

        .category-card,
        .service-card-highlight {
            padding: 2rem 1.5rem;
        }

        .business-title {
            font-size: 2.25rem;
        }

        .business-description {
            font-size: 1.125rem;
        }
    }

    @media (max-width: 576px) {
        .hero-title {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.75rem;
        }

        .search-container .row {
            flex-direction: column;
            gap: 1rem;
        }

        .search-divider {
            display: none;
        }

        .category-card,
        .service-card-highlight {
            padding: 1.5rem 1rem;
        }

        .business-title {
            font-size: 2rem;
        }
    }

    /* Utility Classes */
    .shadow-cw-sm { box-shadow: var(--cw-shadow-sm); }
    .shadow-cw-md { box-shadow: var(--cw-shadow-md); }
    .shadow-cw-lg { box-shadow: var(--cw-shadow-lg); }

    .rounded-cw { border-radius: 0.5rem; }
    .rounded-cw-lg { border-radius: 1rem; }
    .rounded-cw-full { border-radius: 9999px; }
</style>
{% endblock extra_css %}

{% block hero_content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="hero-title">Find & Book<br>Local Spa and Massage Services</h1>
                <p class="hero-subtitle">Discover top-rated wellness venues and book your perfect relaxation experience</p>

                <!-- Search Form -->
                <form action="{% url 'venues_app:venue_search' %}" method="get">
                    <div class="search-container">
                        <div class="row g-0 align-items-center">
                            <div class="col position-relative">
                                <div class="dropdown search-group">
                                    <i class="fas fa-search input-icon-left"></i>
                                    <input type="text" name="query" class="search-input form-control ps-5" data-bs-toggle="dropdown" placeholder="Search treatments..." aria-expanded="false" value="">
                                    <input type="hidden" name="category" value="">
                                    <div class="dropdown-menu treatment-dropdown">
                                        <a class="dropdown-item" href="#" data-category="">
                                            <i class="fas fa-th"></i>
                                            All treatments
                                        </a>
                                        <h6 class="dropdown-header">Top categories</h6>
                                        {% for category in categories %}
                                        <a class="dropdown-item" href="#" data-category="{{ category.id }}">
                                            {% if category.category_name == 'Spa' %}
                                                <i class="fas fa-spa"></i>
                                            {% elif category.category_name == 'Beauty Salon' %}
                                                <i class="fas fa-cut"></i>
                                            {% elif category.category_name == 'Massage' %}
                                                <i class="fas fa-hand-paper"></i>
                                            {% elif category.category_name == 'Fitness & Wellness' %}
                                                <i class="fas fa-dumbbell"></i>
                                            {% elif category.category_name == 'Yoga Studio' %}
                                                <i class="fas fa-leaf"></i>
                                            {% elif category.category_name == 'Medical Spa' %}
                                                <i class="fas fa-stethoscope"></i>
                                            {% elif category.category_name == 'Day Spa' %}
                                                <i class="fas fa-sun"></i>
                                            {% elif category.category_name == 'Wellness Center' %}
                                                <i class="fas fa-heart"></i>
                                            {% else %}
                                                <i class="fas fa-spa"></i>
                                            {% endif %}
                                            {{ category.category_name }}
                                        </a>
                                        {% empty %}
                                        <div class="dropdown-item-text text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            No categories available
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="search-divider"></div>
                            <div class="col position-relative">
                                <div class="dropdown search-group">
                                    <i class="fas fa-map-marker-alt input-icon-left"></i>
                                    <input type="text" name="location" id="id_location" class="search-input form-control ps-5" data-bs-toggle="dropdown" placeholder="City, State, or County" aria-expanded="false" value="" list="locationList" autocomplete="off">
                                    <datalist id="locationList"></datalist>
                                    <div class="dropdown-menu location-dropdown">
                                        <a class="dropdown-item" href="#" id="use-current-location">
                                            <i class="fas fa-location-arrow"></i>
                                            Use current location
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Booking Counter -->
                <div class="booking-count">
                    <strong>425,731</strong> appointments booked today
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block content %}
<!-- Popular Categories Section -->
<section class="popular-categories">
    <div class="container">
        <h2 class="section-title">Popular Categories</h2>
        <p class="section-subtitle">Discover the most sought-after spa and wellness services</p>

        <div class="row g-4">
            {% for category in popular_categories %}
            <div class="col-lg-2 col-md-4 col-sm-6 col-6">
                <a href="{{ category.get_absolute_url }}" class="category-card d-block">
                    <div class="category-icon">
                        {% if category.category_name == 'Spa' %}
                            <i class="fas fa-spa"></i>
                        {% elif category.category_name == 'Beauty Salon' %}
                            <i class="fas fa-cut"></i>
                        {% elif category.category_name == 'Massage' %}
                            <i class="fas fa-hand-paper"></i>
                        {% elif category.category_name == 'Fitness & Wellness' %}
                            <i class="fas fa-dumbbell"></i>
                        {% elif category.category_name == 'Yoga Studio' %}
                            <i class="fas fa-leaf"></i>
                        {% elif category.category_name == 'Medical Spa' %}
                            <i class="fas fa-stethoscope"></i>
                        {% elif category.category_name == 'Day Spa' %}
                            <i class="fas fa-sun"></i>
                        {% elif category.category_name == 'Wellness Center' %}
                            <i class="fas fa-heart"></i>
                        {% else %}
                            <i class="fas fa-spa"></i>
                        {% endif %}
                    </div>
                    <div class="category-name">{{ category.category_name }}</div>
                    <div class="category-count">{{ category.venue_count }} venue{{ category.venue_count|pluralize }}</div>
                </a>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-neutral-cw">No categories available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Service Highlights Section -->
<section class="service-highlights">
    <div class="container">
        <h2 class="section-title">Popular Services</h2>
        <p class="section-subtitle">Book the most popular spa and wellness treatments</p>

        <div class="row g-4">
            {% for service in popular_service_types %}
            <div class="col-lg-4 col-md-6">
                <a href="{% url 'venues_app:venue_search' %}?query={{ service.search_query }}" class="service-card-highlight d-block">
                    <div class="service-icon">
                        <i class="{{ service.icon }}"></i>
                    </div>
                    <div class="service-name">{{ service.name }}</div>
                    <div class="service-description">{{ service.description }}</div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Top Venues Section -->
<section class="venue-sections">
    <div class="container">
        <h2 class="section-title">Top Venues</h2>
        <p class="section-subtitle">Discover the highest-rated venues in your area</p>

        <div class="row g-4">
            {% for venue in top_venues %}
            <div class="col-xl-3 col-lg-4 col-md-6">
                <div class="venue-card card-cw">
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                    </a>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                {{ venue.venue_name }}
                            </a>
                        </h5>
                        <div class="rating">
                            <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                            <span class="review-count">({{ venue.review_count|default:0 }})</span>
                        </div>
                        <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                        <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-neutral-cw">No top venues available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Trending Venues Section -->
<section class="venue-sections">
    <div class="container">
        <h2 class="section-title">Trending Now</h2>
        <p class="section-subtitle">Popular venues with recent activity and great reviews</p>

        <div class="row g-4">
            {% for venue in trending_venues %}
            <div class="col-xl-3 col-lg-4 col-md-6">
                <div class="venue-card card-cw-featured">
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                    </a>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                {{ venue.venue_name }}
                            </a>
                        </h5>
                        <div class="rating">
                            <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                            <span class="review-count">({{ venue.review_count|default:0 }})</span>
                        </div>
                        <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                        <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-neutral-cw">No trending venues available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Special Offers Section -->
<section class="venue-sections">
    <div class="container">
        <h2 class="section-title">Special Offers</h2>
        <p class="section-subtitle">Venues with active discounts and exclusive deals</p>

        <div class="row g-4">
            {% for venue in discounted_venues %}
            <div class="col-xl-3 col-lg-4 col-md-6">
                <div class="venue-card card-cw-accent">
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.venue_name }}">
                    </a>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                                    {{ venue.venue_name }}
                                </a>
                            </h5>
                            <span class="badge bg-success">Special Offer</span>
                        </div>
                        <div class="rating">
                            <span class="rating-score">{{ venue.avg_rating|floatformat:1|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                            <span class="review-count">({{ venue.review_count|default:0 }})</span>
                        </div>
                        <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                        <span class="business-type">{{ venue.categories.first.category_name|default:"Spa & Wellness" }}</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-neutral-cw">No special offers available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

{% if not user.is_customer %}
<!-- Business Section -->
<section class="business-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="business-title display-font">
                    CozyWish<br><span class="text-neutral-cw">For Business</span>
                </h2>
                <p class="business-description">
                    Supercharge your business for free with the world's top booking platform for salons and spas. Independently voted no. 1 by industry professionals.
                </p>
                <div class="d-flex flex-column flex-sm-row gap-3 mb-4">
                    <a href="{% url 'accounts_app:for_business' %}" class="btn-cw-primary">
                        <i class="fas fa-spa me-2"></i>Find out more
                    </a>
                    <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn-cw-secondary">
                        <i class="fas fa-user-plus me-2"></i>Get Started
                    </a>
                </div>
                <div class="rating-container">
                    <h4 class="rating-title">Excellent 5/5</h4>
                    <div class="star-rating mb-2">
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                        <span class="star">★</span>
                    </div>
                    <div class="review-text text-neutral-cw">
                        Over 1250 reviews from our partners
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="business-preview">
                    <img src="https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Spa Business Interface" class="img-fluid rounded-cw-lg shadow-cw-lg">
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
